import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../core/constants/app_colors.dart';
import '../../core/constants/app_constants.dart';
import '../../data/models/category.dart';

class PopularCategoryCard extends StatelessWidget {
  final Category category;
  final VoidCallback? onTap;

  const PopularCategoryCard({super.key, required this.category, this.onTap});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          color: Theme.of(context).brightness == Brightness.dark
              ? AppColors.darkCard
              : Colors.white,
          borderRadius: BorderRadius.circular(AppConstants.radiusCard),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.paddingSmall),
          child: Row(
            children: [
              // Category Icon
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: category.backgroundColor,
                  borderRadius: BorderRadius.circular(
                    AppConstants.radiusMedium,
                  ),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(
                    AppConstants.radiusMedium,
                  ),
                  child: CachedNetworkImage(
                    imageUrl: category.iconUrl,
                    fit: BoxFit.cover,
                    placeholder: (context, url) => Container(
                      color: category.backgroundColor,
                      child: const Center(
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            AppColors.primary,
                          ),
                        ),
                      ),
                    ),
                    errorWidget: (context, url, error) => Container(
                      color: category.backgroundColor,
                      child: Icon(
                        _getCategoryIcon(category.id),
                        color: _getCategoryColor(category.id),
                        size: 24,
                      ),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: AppConstants.paddingSmall),
              // Category Info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      category.name,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 2),
                    Text(
                      '${category.productCount.toString().replaceAllMapped(RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'), (Match m) => '${m[1]},')} items',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  IconData _getCategoryIcon(String categoryId) {
    switch (categoryId) {
      case 'electronics':
        return Icons.devices;
      case 'fashion':
      case 'clothing':
        return Icons.checkroom;
      case 'home':
      case 'home_living':
        return Icons.home;
      case 'beauty':
        return Icons.face;
      case 'sports':
        return Icons.sports;
      case 'toys':
        return Icons.toys;
      default:
        return Icons.category;
    }
  }

  Color _getCategoryColor(String categoryId) {
    switch (categoryId) {
      case 'electronics':
        return AppColors.categoryElectronics;
      case 'fashion':
      case 'clothing':
        return AppColors.categoryClothing;
      case 'home':
      case 'home_living':
        return AppColors.categoryHome;
      case 'beauty':
        return AppColors.categoryBeauty;
      case 'sports':
        return AppColors.categorySports;
      case 'toys':
        return AppColors.categoryToys;
      default:
        return AppColors.primary;
    }
  }
}
