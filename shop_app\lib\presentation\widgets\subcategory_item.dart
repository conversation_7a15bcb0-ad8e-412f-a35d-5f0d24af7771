import 'package:flutter/material.dart';
import '../../core/constants/app_colors.dart';

class SubcategoryItem extends StatelessWidget {
  final String name;
  final VoidCallback? onTap;

  const SubcategoryItem({
    super.key,
    required this.name,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 8),
        child: Text(
          name,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).brightness == Brightness.dark
                    ? AppColors.darkText.withValues(alpha: 0.8)
                    : AppColors.textSecondary,
                fontWeight: FontWeight.w400,
              ),
        ),
      ),
    );
  }
}
